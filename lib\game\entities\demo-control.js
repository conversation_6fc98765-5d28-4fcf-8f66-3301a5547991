/**
 * Docker Component Demo - Demonstrates Docker utility components
 * Shows how to use Docker positioning for UI elements and controls
 */
ig.module('game.entities.demo-control')
.requires(
    'impact.entity',
    'dl-plugins.docker.docker-plugin',
    'plugins.utils.buttons.button-base',
    'plugins.utils.text'
)
.defines(function () {
    EntityDemoControl = ig.Entity.extend(ig.DockerMixin).extend({
        zIndex: 3,
        size: { x: 200, y: 150 },

        // Demo control panel properties
        panelColor: '#2c3e50',
        borderColor: '#34495e',
        textColor: '#ecf0f1',

        // Docker demo entities
        dockeredEntities: [],
        currentDemo: 0,
        demoNames: [
            'Camera Docking',
            'Entity Docking',
            'Corner Positioning',
            'Dynamic Updates'
        ],

        init: function (x, y, settings) {
            this.parent(x, y, settings);

            // Initialize the demo control panel
            this.initDemoPanel();

            // Create demo entities to showcase Docker functionality
            this.createDockerDemoEntities();

            // Start with the first demo
            this.switchToDemo(0);

            // Create control buttons
            this.createControlButtons();

            ig.game.sortEntitiesDeferred();
        },

        initDemoPanel: function () {
            // Dock this control panel to the top-left corner of the screen
            this.dockToCamera({
                percent: { x: 0, y: 0 },
                offset: { x: 20, y: 20 }
            });

            console.log('Docker Demo Control Panel initialized');
            console.log('Docker config:', this.getDockerConfig());
        },

        createDockerDemoEntities: function () {
            // Create various entities to demonstrate Docker positioning

            // 1. Camera-docked UI element (top-right)
            var topRightPanel = ig.game.spawnEntity(EntityDockerDemo, 0, 0, {
                demoType: 'camera-topright',
                color: '#e74c3c',
                label: 'Top Right UI'
            });
            this.dockeredEntities.push(topRightPanel);

            // 2. Camera-docked UI element (bottom-center)
            var bottomPanel = ig.game.spawnEntity(EntityDockerDemo, 0, 0, {
                demoType: 'camera-bottom',
                color: '#3498db',
                label: 'Bottom Center'
            });
            this.dockeredEntities.push(bottomPanel);

            // 3. Entity that docks to another entity
            var followEntity = ig.game.spawnEntity(EntityDockerDemo, 0, 0, {
                demoType: 'entity-follow',
                color: '#f39c12',
                label: 'Follower'
            });
            this.dockeredEntities.push(followEntity);

            // 4. Corner positioning demo
            var cornerEntity = ig.game.spawnEntity(EntityDockerDemo, 0, 0, {
                demoType: 'corners',
                color: '#9b59b6',
                label: 'Corner Demo'
            });
            this.dockeredEntities.push(cornerEntity);
        },

        createControlButtons: function () {
            // Previous demo button
            this.prevButton = ig.game.spawnEntity(EntityDockerButton, 0, 0, {
                text: 'Prev',
                color: '#95a5a6',
                dockerParent: this,
                dockerConfig: {
                    percent: { x: 0, y: 1 },
                    offset: { x: 10, y: 10 }
                },
                onClick: this.previousDemo.bind(this)
            });

            // Next demo button
            this.nextButton = ig.game.spawnEntity(EntityDockerButton, 0, 0, {
                text: 'Next',
                color: '#95a5a6',
                dockerParent: this,
                dockerConfig: {
                    percent: { x: 1, y: 1 },
                    offset: { x: -10, y: 10 }
                },
                onClick: this.nextDemo.bind(this)
            });
        },

        switchToDemo: function (demoIndex) {
            this.currentDemo = demoIndex;

            // Reset all entities to hidden state
            for (var i = 0; i < this.dockeredEntities.length; i++) {
                this.dockeredEntities[i].setVisible(false);
            }

            // Show and configure the current demo
            switch (demoIndex) {
                case 0: // Camera Docking Demo
                    this.showCameraDockingDemo();
                    break;
                case 1: // Entity Docking Demo
                    this.showEntityDockingDemo();
                    break;
                case 2: // Corner Positioning Demo
                    this.showCornerPositioningDemo();
                    break;
                case 3: // Dynamic Updates Demo
                    this.showDynamicUpdatesDemo();
                    break;
            }

            console.log('Switched to demo:', this.demoNames[demoIndex]);
        },

        showCameraDockingDemo: function () {
            // Show camera-docked entities
            this.dockeredEntities[0].setVisible(true); // Top-right panel
            this.dockeredEntities[1].setVisible(true); // Bottom panel

            // Configure their docker settings
            this.dockeredEntities[0].updateDockerConfig({
                dockerObject: ig.game.screen,
                dockerPercent: { x: 1, y: 0 },
                dockerOffset: { x: -20, y: 20 }
            });

            this.dockeredEntities[1].updateDockerConfig({
                dockerObject: ig.game.screen,
                dockerPercent: { x: 0.5, y: 1 },
                dockerOffset: { x: 0, y: -20 }
            });
        },

        showEntityDockingDemo: function () {
            // Show entity following demo
            this.dockeredEntities[2].setVisible(true);

            // Dock the follower to this control panel
            this.dockeredEntities[2].updateDockerConfig({
                dockerObject: this,
                dockerPercent: { x: 1, y: 0.5 },
                dockerOffset: { x: 20, y: 0 }
            });
        },

        showCornerPositioningDemo: function () {
            // Show corner positioning entity
            this.dockeredEntities[3].setVisible(true);

            // Animate through different corner positions
            this.animateCornerPositions();
        },

        showDynamicUpdatesDemo: function () {
            // Show multiple entities with dynamic positioning
            this.dockeredEntities[0].setVisible(true);
            this.dockeredEntities[2].setVisible(true);

            // Start dynamic positioning animation
            this.startDynamicAnimation();
        },

        animateCornerPositions: function () {
            var entity = this.dockeredEntities[3];
            var corners = [
                { percent: { x: 0, y: 0 }, offset: { x: 20, y: 20 } },    // Top-left
                { percent: { x: 1, y: 0 }, offset: { x: -20, y: 20 } },   // Top-right
                { percent: { x: 1, y: 1 }, offset: { x: -20, y: -20 } },  // Bottom-right
                { percent: { x: 0, y: 1 }, offset: { x: 20, y: -20 } }    // Bottom-left
            ];

            var currentCorner = 0;
            var self = this;

            function moveToNextCorner () {
                entity.updateDockerConfig({
                    dockerObject: ig.game.screen,
                    dockerPercent: corners[currentCorner].percent,
                    dockerOffset: corners[currentCorner].offset
                });

                currentCorner = (currentCorner + 1) % corners.length;
                setTimeout(moveToNextCorner, 2000);
            }

            moveToNextCorner();
        },

        startDynamicAnimation: function () {
            var self = this;
            var time = 0;

            this.dynamicTimer = setInterval(function () {
                time += 0.1;

                // Animate the top-right entity in a circular motion
                var radius = 50;
                var centerX = ig.system.width - 100;
                var centerY = 100;

                self.dockeredEntities[0].updateDockerConfig({
                    dockerObject: ig.game.screen,
                    dockerPercent: { x: 0, y: 0 },
                    dockerOffset: {
                        x: centerX + Math.cos(time) * radius,
                        y: centerY + Math.sin(time) * radius
                    }
                });

                // Animate the follower entity
                self.dockeredEntities[2].updateDockerConfig({
                    dockerObject: self,
                    dockerPercent: { x: 0.5, y: 0.5 },
                    dockerOffset: {
                        x: Math.sin(time * 2) * 30,
                        y: Math.cos(time * 2) * 20
                    }
                });
            }, 50);
        },

        previousDemo: function () {
            this.currentDemo = (this.currentDemo - 1 + this.demoNames.length) % this.demoNames.length;
            this.switchToDemo(this.currentDemo);
        },

        nextDemo: function () {
            this.currentDemo = (this.currentDemo + 1) % this.demoNames.length;
            this.switchToDemo(this.currentDemo);
        },

        draw: function () {
            var c = ig.system.context;
            c.save();

            // Draw control panel background
            c.fillStyle = this.panelColor;
            c.fillRect(this.pos.x, this.pos.y, this.size.x, this.size.y);

            // Draw border
            c.strokeStyle = this.borderColor;
            c.lineWidth = 2;
            c.strokeRect(this.pos.x, this.pos.y, this.size.x, this.size.y);

            // Draw title
            c.fillStyle = this.textColor;
            c.font = '16px Arial';
            c.textAlign = 'center';
            c.fillText('Docker Demo', this.pos.x + this.size.x / 2, this.pos.y + 25);

            // Draw current demo name
            c.font = '12px Arial';
            c.fillText(this.demoNames[this.currentDemo], this.pos.x + this.size.x / 2, this.pos.y + 45);

            // Draw demo counter
            c.font = '10px Arial';
            c.fillText((this.currentDemo + 1) + ' / ' + this.demoNames.length,
                      this.pos.x + this.size.x / 2, this.pos.y + 65);

            // Draw instructions
            c.font = '9px Arial';
            c.fillStyle = '#bdc3c7';
            c.fillText('Use Prev/Next buttons', this.pos.x + this.size.x / 2, this.pos.y + 85);
            c.fillText('to switch demos', this.pos.x + this.size.x / 2, this.pos.y + 100);

            c.restore();
        },

        kill: function () {
            // Clean up timers
            if (this.dynamicTimer) {
                clearInterval(this.dynamicTimer);
            }

            this.parent();
        }
    });

    EntityInGame = ig.Entity.extend({
        zIndex: 2,
        image: new ig.Image('media/graphics/sprites/ingame-entity.png'),
        isClickable: true,
        size: {
            x: 267,
            y: 108
        },

        draw: function () {
            /*  ITS POSITION IS RELATIVE TO THE GAME ORIGIN
                HENCE, SUBTRACT IT WITH THE SCREEN POSITION
            */
            this.image.draw(this.pos.x - ig.game.screen.x, this.pos.y - ig.game.screen.y);
        },

        clicked: function () {
            console.log('EntityInGame is clicked.');
        }
    });

    EntityUI = ig.Entity.extend({
        zIndex: 5,
        image: new ig.Image('media/graphics/sprites/ui-entity.png'),
        isClickable: true,

        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.size.x = this.image.width;
            this.size.y = this.image.height;
            this.repos();
        },

        draw: function () {
            /*  ITS POSITION IS RELATIVE TO THE SCREEN ORIGIN
                HENCE, NO NEED TO SUBTRACT IT WITH THE SCREEN POSITION
            */
            this.image.draw(this.pos.x, this.pos.y);
        },

        repos: function () {
            this.pos.x = ig.system.width - this.image.width - 15;
            this.pos.y = ig.system.height - this.image.height - 15;
        },

        clicked: function () {
            console.log('EntityUI is clicked.');
        },

        /* OVERRIDE DEFAULT METHOD */
        underPointer: function () {
            var p = ig.game.io.getClickPos();
            return this.containPoint(p);
        }
    });

    /* ---------- DRAW A BACKGROUND THAT COVER THE WHOLE SCREEN WITHOUT STRETCHING ---------- */
    EntityCoverBackground = ig.Entity.extend({
        zIndex: 1,
        image: new ig.Image('media/graphics/sprites/background.png'),

        init: function (x, y, settings) {
            this.repos();
        },

        draw: function () {
            ig.system.context.drawImage(this.image.data, this.bgX, this.bgY, this.bgW, this.bgH, 0, 0, ig.system.width, ig.system.height);
        },

        repos: function () {
            var r1 = this.image.width / this.image.height;
                var r2 = ig.system.width / ig.system.height;
            if (r1 > r2) {
                this.bgH = this.image.height;
                this.bgW = this.bgH * r2;
                this.bgX = (this.image.width - this.bgW) / 2;
                this.bgY = 0;
            } else {
                this.bgW = this.image.width;
                this.bgH = this.bgW / r2;
                this.bgX = 0;
                this.bgY = (this.image.height - this.bgH) / 2;
            }
        }
    });
});
